/**
 * Audio Player Component
 * Plays audio files shared in chat with play/pause controls and progress indicator
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Audio } from 'expo-av';
import { MediaAttachment } from '../services/MediaService';

interface AudioPlayerProps {
  attachment: MediaAttachment;
  isOwnMessage?: boolean;
}

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  attachment,
  isOwnMessage = false,
}) => {
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [duration, setDuration] = useState<number | null>(null);
  const [position, setPosition] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  
  const progressAnim = useRef(new Animated.Value(0)).current;
  const positionUpdateInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (sound) {
        sound.unloadAsync();
      }
      if (positionUpdateInterval.current) {
        clearInterval(positionUpdateInterval.current);
      }
    };
  }, [sound]);

  const formatTime = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const loadAudio = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      // Configure audio mode for playback
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: attachment.uri },
        { shouldPlay: false, isLooping: false },
        onPlaybackStatusUpdate
      );

      setSound(newSound);
    } catch (error) {
      console.error('Error loading audio:', error);
      setError('Failed to load audio');
    } finally {
      setIsLoading(false);
    }
  };

  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      setDuration(status.durationMillis);
      setPosition(status.positionMillis || 0);
      setIsPlaying(status.isPlaying);

      // Update progress animation
      if (status.durationMillis && status.durationMillis > 0) {
        const progress = (status.positionMillis || 0) / status.durationMillis;
        Animated.timing(progressAnim, {
          toValue: progress,
          duration: 100,
          useNativeDriver: false,
        }).start();
      }

      // Handle playback completion
      if (status.didJustFinish) {
        setIsPlaying(false);
        setPosition(0);
        progressAnim.setValue(0);
      }
    } else if (status.error) {
      console.error('Audio playback error:', status.error);
      setError('Playback error');
      setIsPlaying(false);
    }
  };

  const togglePlayback = async (): Promise<void> => {
    try {
      if (!sound) {
        await loadAudio();
        return;
      }

      if (isPlaying) {
        await sound.pauseAsync();
      } else {
        await sound.playAsync();
      }
    } catch (error) {
      console.error('Error toggling playback:', error);
      setError('Playback failed');
    }
  };

  const seekTo = async (progress: number): Promise<void> => {
    if (sound && duration) {
      try {
        const newPosition = progress * duration;
        await sound.setPositionAsync(newPosition);
      } catch (error) {
        console.error('Error seeking:', error);
      }
    }
  };

  const getPlayButtonIcon = (): string => {
    if (isLoading) return '⏳';
    if (error) return '⚠️';
    return isPlaying ? '⏸️' : '▶️';
  };

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
    extrapolate: 'clamp',
  });

  return (
    <View style={[
      styles.container,
      isOwnMessage ? styles.ownMessage : styles.receivedMessage
    ]}>
      {/* Audio file info */}
      <View style={styles.header}>
        <Text style={styles.audioIcon}>🎵</Text>
        <View style={styles.fileInfo}>
          <Text style={[
            styles.fileName,
            isOwnMessage ? styles.ownText : styles.receivedText
          ]} numberOfLines={1}>
            {attachment.name}
          </Text>
          <Text style={[
            styles.fileSize,
            isOwnMessage ? styles.ownSubText : styles.receivedSubText
          ]}>
            {attachment.size > 1024 * 1024 
              ? `${(attachment.size / (1024 * 1024)).toFixed(1)} MB`
              : `${Math.round(attachment.size / 1024)} KB`}
          </Text>
        </View>
      </View>

      {/* Player controls */}
      <View style={styles.playerControls}>
        <TouchableOpacity
          style={[
            styles.playButton,
            isOwnMessage ? styles.ownPlayButton : styles.receivedPlayButton
          ]}
          onPress={togglePlayback}
          disabled={isLoading}
        >
          <Text style={styles.playButtonText}>
            {getPlayButtonIcon()}
          </Text>
        </TouchableOpacity>

        <View style={styles.progressContainer}>
          <View style={[
            styles.progressTrack,
            isOwnMessage ? styles.ownProgressTrack : styles.receivedProgressTrack
          ]}>
            <Animated.View style={[
              styles.progressBar,
              isOwnMessage ? styles.ownProgressBar : styles.receivedProgressBar,
              { width: progressWidth }
            ]} />
          </View>
          
          <View style={styles.timeContainer}>
            <Text style={[
              styles.timeText,
              isOwnMessage ? styles.ownSubText : styles.receivedSubText
            ]}>
              {formatTime(position)}
            </Text>
            {duration && (
              <Text style={[
                styles.timeText,
                isOwnMessage ? styles.ownSubText : styles.receivedSubText
              ]}>
                {formatTime(duration)}
              </Text>
            )}
          </View>
        </View>
      </View>

      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 12,
    minWidth: 200,
    maxWidth: 280,
  },
  ownMessage: {
    backgroundColor: '#007AFF',
  },
  receivedMessage: {
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  audioIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 14,
    fontWeight: '500',
  },
  fileSize: {
    fontSize: 12,
    marginTop: 2,
  },
  ownText: {
    color: 'white',
  },
  receivedText: {
    color: '#000',
  },
  ownSubText: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  receivedSubText: {
    color: '#666',
  },
  playerControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  playButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  ownPlayButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  receivedPlayButton: {
    backgroundColor: '#007AFF',
  },
  playButtonText: {
    fontSize: 16,
    color: 'white',
  },
  progressContainer: {
    flex: 1,
  },
  progressTrack: {
    height: 4,
    borderRadius: 2,
    marginBottom: 4,
  },
  ownProgressTrack: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  receivedProgressTrack: {
    backgroundColor: '#E0E0E0',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  ownProgressBar: {
    backgroundColor: 'white',
  },
  receivedProgressBar: {
    backgroundColor: '#007AFF',
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeText: {
    fontSize: 11,
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
});
