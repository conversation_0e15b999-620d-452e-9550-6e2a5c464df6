/**
 * MediaCacheManager - Production-grade WhatsApp-like media persistence
 * Handles local storage, upload states, and reliable persistence for iOS/Android
 * Uses documents directory for true persistence (prevents media disappearing)
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { MediaAttachment } from './MediaService';

export interface CachedMedia {
  id: string;
  localUri: string;
  originalUri: string;
  uploadStatus: 'pending' | 'uploading' | 'uploaded' | 'failed';
  uploadProgress?: number; // 0-100 percentage
  serverUri?: string;
  timestamp: number;
  mimeType: string;
  isImage: boolean;
  isVideo: boolean;
  name: string;
  size: number;
  thumbnailUri?: string; // Local video thumbnail for instant preview
}

export class MediaCacheManager {
  private static instance: MediaCacheManager;
  private cache: Map<string, CachedMedia> = new Map();
  
  // Directories for permanent storage vs temporary cache
  private mediaDir: string; // For permanent storage - like WhatsApp "Media" folder
  private thumbsDir: string; // For thumbnails - also permanent
  private tempDir: string; // For true temporary files
  
  private constructor() {
    // Create a WhatsApp-like directory structure for better media management
    this.mediaDir = `${FileSystem.documentDirectory}CCALC_Media/`; // Permanent storage in documents directory
    this.thumbsDir = `${FileSystem.documentDirectory}CCALC_Media/Thumbnails/`; // Thumbnails storage
    this.tempDir = `${FileSystem.cacheDirectory}CCALC_Temp/`; // True temporary files can use cache
    
    this.initializeStorage();
  }

  public static getInstance(): MediaCacheManager {
    if (!MediaCacheManager.instance) {
      MediaCacheManager.instance = new MediaCacheManager();
    }
    return MediaCacheManager.instance;
  }

  /**
   * Initialize storage directories and load existing cache
   */
  private async initializeStorage(): Promise<void> {
    try {
      // Create all required directories if they don't exist
      await this.ensureDirectoryExists(this.mediaDir);
      await this.ensureDirectoryExists(this.thumbsDir);
      await this.ensureDirectoryExists(this.tempDir);
      
      console.log('📂 Media directories initialized:', {
        mediaDir: this.mediaDir,
        thumbsDir: this.thumbsDir,
        tempDir: this.tempDir
      });

      // Load existing cache from AsyncStorage
      const cachedData = await AsyncStorage.getItem('media_cache');
      if (cachedData) {
        const cacheArray: CachedMedia[] = JSON.parse(cachedData);
        cacheArray.forEach(item => {
          this.cache.set(item.id, item);
        });
        console.log('📦 MediaCache loaded:', this.cache.size, 'items');
      }

      // Clean up old cache entries (older than 7 days)
      await this.cleanupOldCache();
    } catch (error) {
      console.error('MediaCache initialization failed:', error);
    }
  }
  
  /**
   * Helper to ensure a directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(dirPath);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(dirPath, { intermediates: true });
        console.log('📁 Created directory:', dirPath);
      }
    } catch (error) {
      console.error(`Failed to create directory ${dirPath}:`, error);
      throw error;
    }
  }

  /**
   * Store media for persistent usage with WhatsApp-like organization
   * This ensures media won't disappear when iOS clears cache
   */
  public async storeMedia(attachment: MediaAttachment): Promise<CachedMedia> {
    // Use a more predictable ID format without random strings
    const timestamp = Date.now().toString();
    const mediaId = attachment.id || `media_${timestamp.substring(8)}`;
    
    // Clean up the filename to be more WhatsApp-like (no random UUIDs)
    const cleanName = attachment.name
      .replace(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i, '')
      .replace(/\d+_\w+/g, '')
      .trim();
      
    // Ensure proper file extension for media type
    const getFileExtension = (uri: string, mimeType?: string) => {
      // Try to get extension from URI
      const uriExt = uri.split('.').pop()?.toLowerCase();
      if (uriExt && ['jpg', 'jpeg', 'png', 'gif', 'heic', 'mp4', 'mov', 'mp3', 'm4a'].includes(uriExt)) {
        return uriExt;
      }
      
      // Fallback to mimetype
      if (mimeType) {
        switch(mimeType) {
          case 'image/jpeg': return 'jpg';
          case 'image/png': return 'png';
          case 'image/gif': return 'gif';
          case 'image/heic': return 'heic';
          case 'video/mp4': return 'mp4';
          case 'video/quicktime': return 'mov';
          case 'audio/mpeg': return 'mp3';
          case 'audio/mp4': return 'm4a';
          default:
            if (mimeType.startsWith('image/')) return 'jpg';
            if (mimeType.startsWith('video/')) return 'mp4';
            if (mimeType.startsWith('audio/')) return 'mp3';
        }
      }
      
      // Final fallback
      return attachment.isImage ? 'jpg' : 
             attachment.isVideo ? 'mp4' : 
             attachment.isAudio ? 'mp3' : 'bin';
    };
    
    const fileExt = getFileExtension(attachment.uri, attachment.mimeType);
    const fileName = `${mediaId}.${fileExt}`;
    
    // Store in the permanent storage directory (instead of cache)
    const localPath = `${this.mediaDir}${fileName}`;

    try {
      // Validate input URI
      if (!attachment.uri || attachment.uri.trim() === '') {
        throw new Error('Invalid attachment URI');
      }

      console.log('📦 Storing media:', {
        mediaId,
        fileName,
        originalUri: attachment.uri.substring(0, 80) + (attachment.uri.length > 80 ? '...' : ''),
        size: attachment.size,
        isImage: attachment.isImage,
        isVideo: attachment.isVideo
      });

      // First verify the source file exists and is accessible
      try {
        const sourceInfo = await FileSystem.getInfoAsync(attachment.uri);
        if (!sourceInfo.exists && !attachment.uri.startsWith('ph://') && !attachment.uri.startsWith('content://')) {
          console.warn('⚠️ Source file does not exist:', attachment.uri);
          // Continue anyway as URI might be a content URI or photo library URI
        }
        
        console.log('📦 Source file info:', 
          sourceInfo.exists ? 
            `Size: ${(sourceInfo.size / (1024 * 1024)).toFixed(2)} MB` : 
            'Using content/photo URI');
      } catch (sourceError) {
        console.warn('⚠️ Error checking source file:', sourceError);
        // Continue anyway, copyAsync might still work with content URIs
      }
      
      // Ensure all required directories exist
      await this.ensureDirectoryExists(this.mediaDir);
      
      // Copy file to our permanent media directory with robust error handling
      try {
        await FileSystem.copyAsync({
          from: attachment.uri,
          to: localPath,
        });
        
        // Verify the copy was successful
        const fileInfo = await FileSystem.getInfoAsync(localPath);
        if (!fileInfo.exists) {
          throw new Error('Failed to copy file to media directory');
        }
        
        console.log('✅ File copied successfully to media directory:', {
          path: localPath,
          size: fileInfo.size ? `${(fileInfo.size / (1024 * 1024)).toFixed(2)} MB` : 'unknown'
        });
      } catch (copyError) {
        console.error('❌ Failed to copy file to media directory:', copyError);
        // We'll continue and use the original URI as fallback
      }

      let thumbnailUri: string | undefined = undefined;
      if (attachment.isVideo) {
        try {
          console.log('🎬 Generating video thumbnail for:', attachment.name);
          
          // Try multiple times with different time offsets if needed
          let thumbUri = null;
          const timeOffsets = [0, 1000, 3000]; // Try at start, 1s, and 3s
          
          for (const timeOffset of timeOffsets) {
            try {
              const result = await VideoThumbnails.getThumbnailAsync(attachment.uri, { 
                time: timeOffset,
                quality: 0.7
              });
              thumbUri = result.uri;
              console.log(`✅ Thumbnail generated at ${timeOffset}ms offset`);
              break; // Exit loop if successful
            } catch (innerError) {
              console.warn(`⚠️ Failed thumbnail at ${timeOffset}ms:`, innerError);
              // Try next offset
            }
          }
          
          if (!thumbUri) {
            throw new Error('All thumbnail generation attempts failed');
          }
          
          // Copy thumbnail to thumbnails dir for persistence
          const thumbFileName = `${mediaId}_thumb.jpg`;
          const thumbPath = `${this.thumbsDir}${thumbFileName}`;
          
          // Ensure thumbnail directory exists
          await this.ensureDirectoryExists(this.thumbsDir);
          
          await FileSystem.copyAsync({ from: thumbUri, to: thumbPath });
          
          // Verify thumbnail copy
          const thumbInfo = await FileSystem.getInfoAsync(thumbPath);
          if (thumbInfo.exists) {
            thumbnailUri = thumbPath;
            console.log('📦 Video thumbnail stored permanently:', thumbFileName);
          }
        } catch (err) {
          console.warn('⚠️ Video thumbnail generation failed:', err);
        }
      }

      const cachedMedia: CachedMedia = {
        id: mediaId,
        localUri: localPath,
        originalUri: attachment.uri,
        uploadStatus: 'pending',
        timestamp: Date.now(),
        mimeType: attachment.mimeType || 'application/octet-stream',
        isImage: attachment.isImage || false,
        isVideo: attachment.isVideo || false,
        name: attachment.name,
        size: attachment.size,
        thumbnailUri,
      };

      // Store in memory cache
      this.cache.set(mediaId, cachedMedia);

      // Persist to AsyncStorage
      await this.persistCache();

      console.log('✅ Media cached successfully:', {
        mediaId,
        fileName,
        localPath: localPath.substring(localPath.lastIndexOf('/') + 1),
        hasThumbnail: !!thumbnailUri,
        cacheSize: this.cache.size
      });
      
      return cachedMedia;
    } catch (error) {
      console.error('Failed to cache media:', error);
      throw error;
    }
  }

  /**
   * Cache media for instant preview - WhatsApp-like experience
   * Returns an attachment with cached local URI for immediate display
   */
  public async cacheMediaForInstantPreview(attachment: MediaAttachment): Promise<MediaAttachment> {
    try {
      // Only cache images and videos
      if (!attachment.isImage && !attachment.isVideo) {
        return attachment;
      }

      const cachedMedia = await this.storeMedia(attachment);
      
      // Return updated attachment with cached URI and thumbnail
      return {
        ...attachment,
        uri: cachedMedia.localUri,
        id: cachedMedia.id,
        thumbnailUri: cachedMedia.thumbnailUri,
      };
    } catch (error) {
      console.error('Failed to cache media for instant preview:', error);
      // Return original attachment as fallback
      return attachment;
    }
  }

  /**
   * Get media for display - returns local URI for instant preview
   */
  public getMedia(mediaId: string): CachedMedia | null {
    return this.cache.get(mediaId) || null;
  }

  /**
   * Update upload status
   */
  public async updateUploadStatus(
    mediaId: string,
    status: CachedMedia['uploadStatus'],
    serverUri?: string
  ): Promise<void> {
    const media = this.cache.get(mediaId);
    if (media) {
      media.uploadStatus = status;
      if (serverUri) {
        media.serverUri = serverUri;
      }
      // Reset progress when upload completes or fails
      if (status === 'uploaded' || status === 'failed') {
        media.uploadProgress = undefined;
      }
      this.cache.set(mediaId, media);
      await this.persistCache();
      console.log('📦 Upload status updated:', mediaId, status);
    }
  }

  /**
   * Create a server media cache entry that points to the same local file
   * This allows the app to find media by server ID
   */
  public async createServerMediaEntry(localMediaId: string, serverMediaId: string, serverUri: string): Promise<void> {
    const localMedia = this.cache.get(localMediaId);
    if (localMedia) {
      // Create a new cache entry with server ID that points to the same local file
      const serverMediaEntry: CachedMedia = {
        ...localMedia,
        id: serverMediaId,
        serverUri: serverUri
      };
      this.cache.set(serverMediaId, serverMediaEntry);
      await this.persistCache();
      console.log('📦 Created server media cache entry:', {
        localId: localMediaId,
        serverId: serverMediaId,
        serverUri: serverUri
      });
    }
  }

  /**
   * Update upload progress (WhatsApp-like progress indicator)
   */
  public async updateUploadProgress(mediaId: string, progress: number): Promise<void> {
    const media = this.cache.get(mediaId);
    if (media && media.uploadStatus === 'uploading') {
      media.uploadProgress = Math.min(100, Math.max(0, progress));
      this.cache.set(mediaId, media);
      // Don't persist every progress update to avoid performance issues
      console.log('📊 Upload progress:', mediaId, `${progress}%`);
    }
  }

  /**
   * Get the best URI for display - ALWAYS returns valid URI to prevent disappearing
   * CRITICAL: This ensures media NEVER disappears from UI during upload
   * Enhanced with multi-level fallback for reliability across all environments
   */
  public async getDisplayUri(mediaId: string): Promise<string | null> {
    const media = this.cache.get(mediaId);
    if (!media) {
      console.warn('⚠️ No cached media found with ID:', mediaId);
      return null;
    }

    // Use a waterfall approach to find the best available URI
    
    // Try local cached file first (best performance + persistence)
    if (media.localUri && media.localUri.trim() !== '') {
      try {
        const fileInfo = await FileSystem.getInfoAsync(media.localUri);
        if (fileInfo.exists && fileInfo.size > 0) {
          console.log('✅ Using local cached file:', media.localUri.split('/').pop(), `(${fileInfo.size} bytes)`);
          return media.localUri;
        } else {
          console.warn('⚠️ Local cached file missing or empty:', media.localUri, 
            `Exists: ${fileInfo.exists}, Size: ${fileInfo.exists ? (fileInfo as any).size || 'unknown' : 'N/A'}`);
        }
      } catch (error) {
        console.warn('⚠️ Error checking local cached file:', error);
      }
    }
    
    // Try server URI next (if available)
    if (media.serverUri && media.serverUri.trim() !== '') {
      console.log('✅ Using server URI as fallback');
      return media.serverUri;
    }
    
    // Final fallback to original URI
    if (media.originalUri && media.originalUri.trim() !== '') {
      console.log('✅ Using original URI as final fallback');
      return media.originalUri;
    }
    
    // Return local URI anyway as last resort
    console.warn('⚠️ All URI options failed, returning whatever we have');
    return media.localUri || null;
  }

  /**
   * Synchronous version for immediate UI rendering with aggressive fallback
   * This must NEVER return null/undefined for a valid mediaId to prevent disappearing media
   */
  public getDisplayUriSync(mediaId: string): string | null {
    const media = this.cache.get(mediaId);
    if (!media) {
      console.warn('⚠️ No cached media found with ID (sync):', mediaId);
      return null;
    }
    
    // In sync mode, use any available URI in priority order
    // This ensures something always shows, even if it's not ideal
    
    // For videos, prefer thumbnail if available
    if (media.isVideo && media.thumbnailUri && 
        media.thumbnailUri.trim() !== '' && 
        (media.thumbnailUri.startsWith('file://') || media.thumbnailUri.startsWith('/'))) {
      console.log('✅ Using video thumbnail URI (sync):', media.thumbnailUri.split('/').pop());
      return media.thumbnailUri;
    }
    
    // If local URI exists and seems valid (starts with file://)
    if (media.localUri && 
        media.localUri.trim() !== '' && 
        (media.localUri.startsWith('file://') || media.localUri.startsWith('/'))) {
      console.log('✅ Using local URI (sync):', media.localUri.split('/').pop());
      return media.localUri;
    }
    
    // If we have a server URI, use that
    if (media.serverUri && media.serverUri.trim() !== '') {
      console.log('✅ Using server URI (sync):', media.serverUri.substring(0, 50) + '...');
      return media.serverUri;
    }
    
    // Last resort - original URI
    if (media.originalUri && media.originalUri.trim() !== '') {
      console.log('⚠️ Using original URI as last resort (sync):', media.originalUri.substring(0, 50) + '...');
      return media.originalUri;
    }
    
    // If we somehow have a mediaId but no URIs at all
    console.error('❌ Critical: Media has no valid URIs:', mediaId);
    return null;
  }

  /**
   * Get server URI if available (for download links, etc.)
   */
  public getServerUri(mediaId: string): string | null {
    const media = this.cache.get(mediaId);
    return media?.serverUri || null;
  }

  /**
   * Get upload status and info for UI indicators
   */
  public getCachedMediaInfo(mediaId: string): { 
    uploadStatus: CachedMedia['uploadStatus'], 
    uploadProgress?: number,
    serverUri?: string,
    localUri?: string 
  } | null {
    const media = this.cache.get(mediaId);
    if (!media) return null;
    
    return {
      uploadStatus: media.uploadStatus,
      uploadProgress: media.uploadProgress,
      serverUri: media.serverUri,
      localUri: media.localUri,
    };
  }

  /**
   * Check if media exists locally
   */
  public async isMediaAvailable(mediaId: string): Promise<boolean> {
    const media = this.cache.get(mediaId);
    if (!media) return false;

    try {
      const fileInfo = await FileSystem.getInfoAsync(media.localUri);
      return fileInfo.exists;
    } catch {
      return false;
    }
  }

  /**
   * Persist cache to AsyncStorage
   */
  private async persistCache(): Promise<void> {
    try {
      const cacheArray = Array.from(this.cache.values());
      await AsyncStorage.setItem('media_cache', JSON.stringify(cacheArray));
    } catch (error) {
      console.error('Failed to persist cache:', error);
    }
  }

  /**
   * Clean up old cache entries
   * WhatsApp-like cleanup that keeps files users might still need
   */
  private async cleanupOldCache(): Promise<void> {
    try {
      // Longer retention for actual media (30 days) like WhatsApp does
      const mediaRetentionTime = 30 * 24 * 60 * 60 * 1000; // 30 days for media
      const thumbRetentionTime = 30 * 24 * 60 * 60 * 1000; // 30 days for thumbnails
      const tempRetentionTime = 7 * 24 * 60 * 60 * 1000;  // 7 days for temp files
      
      const now = Date.now();
      const toDelete: string[] = [];

      // Identify old entries in the cache registry
      for (const [id, media] of this.cache) {
        const age = now - media.timestamp;
        
        // Skip important media that should be kept
        if (media.uploadStatus === 'uploaded' && age < mediaRetentionTime) {
          continue; // Keep recently uploaded media
        }
        
        // Handle failed uploads - keep for retry opportunity
        if (media.uploadStatus === 'failed' && age < mediaRetentionTime / 2) {
          continue; // Keep failed uploads for potential retry
        }
        
        // For old entries, delete the associated files
        toDelete.push(id);
        
        // Delete media file
        if (media.localUri && media.localUri.startsWith('file://')) {
          try {
            await FileSystem.deleteAsync(media.localUri, { idempotent: true });
            console.log('🗑️ Deleted old media file:', media.localUri.split('/').pop());
          } catch (error) {
            console.warn('Failed to delete cached file:', media.localUri);
          }
        }
        
        // Delete thumbnail if it exists
        if (media.thumbnailUri && media.thumbnailUri.startsWith('file://')) {
          try {
            await FileSystem.deleteAsync(media.thumbnailUri, { idempotent: true });
            console.log('🗑️ Deleted old thumbnail:', media.thumbnailUri.split('/').pop());
          } catch (error) {
            console.warn('Failed to delete thumbnail:', media.thumbnailUri);
          }
        }
      }

      // Remove from registry cache
      toDelete.forEach(id => this.cache.delete(id));

      if (toDelete.length > 0) {
        await this.persistCache();
        console.log('📦 Cleaned up', toDelete.length, 'old media entries');
      }
      
      // Additionally clean up orphaned temp files (files without entries in cache)
      await this.cleanupOrphanedFiles();
      
    } catch (error) {
      console.error('Error during cache cleanup:', error);
    }
  }
  
  /**
   * Clean up orphaned files that have no entry in the cache registry
   */
  private async cleanupOrphanedFiles(): Promise<void> {
    try {
      // Helper to get all files in a directory
      const getFilesInDir = async (dirPath: string): Promise<string[]> => {
        try {
          const dirExists = await FileSystem.getInfoAsync(dirPath);
          if (!dirExists.exists) return [];
          
          return await FileSystem.readDirectoryAsync(dirPath);
        } catch (error) {
          console.error(`Failed to read directory ${dirPath}:`, error);
          return [];
        }
      };
      
      // Get all current media IDs from cache
      const validMediaIds = new Set(Array.from(this.cache.values()).map(media => {
        // Extract just the filename without path and extension
        const mediaId = media.id;
        return mediaId;
      }));
      
      // Check media directory
      const mediaFiles = await getFilesInDir(this.mediaDir);
      for (const file of mediaFiles) {
        // Extract media ID from filename (typically mediaId.ext format)
        const mediaId = file.split('.')[0];
        
        if (!validMediaIds.has(mediaId)) {
          // This is an orphaned file
          try {
            await FileSystem.deleteAsync(`${this.mediaDir}${file}`, { idempotent: true });
            console.log('🗑️ Deleted orphaned media file:', file);
          } catch (error) {
            console.warn(`Failed to delete orphaned file ${file}:`, error);
          }
        }
      }
      
      // Check thumbnails directory
      const thumbFiles = await getFilesInDir(this.thumbsDir);
      for (const file of thumbFiles) {
        // Extract media ID from thumbnail filename (typically mediaId_thumb.ext format)
        const mediaId = file.split('_')[0];
        
        if (!validMediaIds.has(mediaId)) {
          // This is an orphaned thumbnail
          try {
            await FileSystem.deleteAsync(`${this.thumbsDir}${file}`, { idempotent: true });
            console.log('🗑️ Deleted orphaned thumbnail:', file);
          } catch (error) {
            console.warn(`Failed to delete orphaned thumbnail ${file}:`, error);
          }
        }
      }
      
      // Always cleanup temp directory aggressively
      const tempFiles = await getFilesInDir(this.tempDir);
      if (tempFiles.length > 0) {
        for (const file of tempFiles) {
          try {
            await FileSystem.deleteAsync(`${this.tempDir}${file}`, { idempotent: true });
          } catch (error) {
            // Ignore errors for temp files
          }
        }
        console.log('🗑️ Cleared', tempFiles.length, 'temporary files');
      }
      
    } catch (error) {
      console.error('Error cleaning orphaned files:', error);
    }
  }

  /**
   * Get all pending uploads for background processing
   */
  public getPendingUploads(): CachedMedia[] {
    return Array.from(this.cache.values()).filter(
      media => media.uploadStatus === 'pending' || media.uploadStatus === 'failed'
    );
  }

  /**
   * Clear all cache (for debugging/testing)
   */
  public async clearCache(): Promise<void> {
    try {
      // Delete all directories
      await FileSystem.deleteAsync(this.mediaDir, { idempotent: true });
      await FileSystem.deleteAsync(this.thumbsDir, { idempotent: true });
      await FileSystem.deleteAsync(this.tempDir, { idempotent: true });
      
      // Recreate directories
      await this.ensureDirectoryExists(this.mediaDir);
      await this.ensureDirectoryExists(this.thumbsDir);
      await this.ensureDirectoryExists(this.tempDir);
      
      // Clear memory cache
      this.cache.clear();
      
      // Clear persistent storage
      await AsyncStorage.removeItem('media_cache');
      
      console.log('📦 All media cache cleared');
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }
}
