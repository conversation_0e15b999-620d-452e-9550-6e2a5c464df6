import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import UserModel from '../../models/User';
import VoiceCallModel, { IVoiceCallModel } from '../../models/VoiceCall';
import VoiceProfileModel from '../../models/VoiceProfile';
import voiceModulationService, { VOICE_PROFILES } from '../../services/voiceModulation';
import multer from 'multer';
import mongoose from 'mongoose';
import path from 'path';
import fs from 'fs';

const router = Router();

/**
 * Start Voice Call
 * POST /api/voice/start-call
 */
router.post('/start-call', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callType = 'mobile_to_superuser', timestamp } = req.body;
    const callerId = (req as any).user?.id;
    const username = (req as any).user?.username;

    if (!callerId) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    console.log('📞 Starting voice call:', { callerId, username, callType });

    // Find superuser as recipient
    const superuser = await UserModel.findOne({ isSuperuser: true, status: 'active' });
    if (!superuser) {
      res.status(404).json({
        success: false,
        error: 'No active superuser found'
      });
      return;
    }

    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const recipientId = (superuser._id as any).toString();

    // Create voice call record with automatic recording enabled
    const voiceCall = await VoiceCallModel.create({
      callId,
      callerId,
      recipientId,
      type: callType,
      status: 'initiated',
      startTime: new Date(timestamp || Date.now()),
      morphingProfile: 'user_secure', // Default profile for users
      callerIP: req.ip || 'unknown',
      deviceFingerprint: req.headers['x-device-fingerprint'] as string || 'unknown',
      bleDeviceId: req.headers['x-ble-device-id'] as string,
      metadata: {
        callerUserAgent: req.get('User-Agent') || 'mobile-app',
        platform: 'mobile',
        deviceType: 'unknown',
        networkType: 'unknown',
        encryptionLevel: 'AES-256',
        compressionRatio: 0.7,
        audioCodec: 'AAC',
        // Recording is enabled by default for all calls (compliance requirement)
        recordingEnabled: true,
        recordingNotified: false // Users are NOT notified about recording
      }
    });

    // Log for audit
    await AuditLogModel.create({
      action: 'VOICE_CALL_STARTED',
      userId: callerId,
      details: {
        callId,
        recipientId,
        callType,
        morphingProfile: voiceCall.morphingProfile
      },
      severity: 'medium',
      category: 'voice_communication',
      ipAddress: '0.0.0.0', // Will be set by middleware if available
      userAgent: 'mobile-app'
    });

    console.log('✅ Voice call started:', callId);

    res.json({
      success: true,
      callId,
      status: 'initiated',
      morphingProfile: voiceCall.morphingProfile,
      recordingEnabled: true // Always enabled for compliance
    });

  } catch (error: any) {
    console.error('Start call error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start call'
    });
  }
});

/**
 * Connect Voice Call and Start Recording
 * POST /api/voice/connect-call
 */
router.post('/connect-call', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.body;
    const userId = (req as any).user?.id;

    if (!callId) {
      res.status(400).json({
        success: false,
        error: 'Call ID is required'
      });
      return;
    }

    // Find the voice call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Voice call not found'
      });
      return;
    }

    // Update call status to connected
    voiceCall.status = 'connected';

    // SECURITY: Automatically start recording (hidden from users)
    if (!voiceCall.recordingPath) {
      const recordingDir = path.join(__dirname, '../../uploads/recordings/encrypted');
      if (!fs.existsSync(recordingDir)) {
        fs.mkdirSync(recordingDir, { recursive: true });
      }

      const recordingFileName = `call_${callId}_${Date.now()}.enc`;
      const recordingPath = path.join(recordingDir, recordingFileName);

      voiceCall.recordingPath = recordingPath;
      voiceCall.metadata = {
        ...voiceCall.metadata,
        recordingStarted: new Date(),
        recordingEnabled: true,
        recordingNotified: false // Users are NOT notified
      };
    }

    await voiceCall.save();

    // Log for audit (recording start is logged but not exposed to users)
    await AuditLogModel.create({
      action: 'VOICE_CALL_CONNECTED_RECORDING_STARTED',
      userId,
      details: {
        callId,
        recordingPath: voiceCall.recordingPath,
        morphingProfile: voiceCall.morphingProfile,
        recordingHidden: true // Flag that recording is hidden from user
      },
      severity: 'medium',
      category: 'voice_communication',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    console.log('✅ Voice call connected with hidden recording:', callId);

    res.json({
      success: true,
      callId,
      status: 'connected',
      morphingProfile: voiceCall.morphingProfile
      // Note: recordingEnabled is NOT returned to hide from users
    });

  } catch (error: any) {
    console.error('Connect call error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to connect call'
    });
  }
});

/**
 * End Voice Call
 * POST /api/voice/end-call
 */
router.post('/end-call', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId, duration, endTime } = req.body;
    const callerId = (req as any).user?.id;

    if (!callId) {
      res.status(400).json({
        success: false,
        error: 'Call ID is required'
      });
      return;
    }

    console.log('📞 Ending voice call:', { callId, callerId, duration });

    // Find and update voice call
    const voiceCall = await VoiceCallModel.findOneAndUpdate(
      { callId, callerId },
      {
        status: 'completed',
        endTime: new Date(endTime || Date.now()),
        duration: duration || 0
      },
      { new: true }
    );

    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Voice call not found'
      });
      return;
    }

    // Log for audit
    await AuditLogModel.create({
      action: 'VOICE_CALL_ENDED',
      userId: callerId,
      details: {
        callId,
        duration: voiceCall.duration || 0,
        hasRecording: !!voiceCall.recordingPath
      },
      severity: 'low',
      category: 'voice_communication',
      ipAddress: '0.0.0.0',
      userAgent: 'mobile-app'
    });

    console.log('✅ Voice call ended:', callId);

    res.json({
      success: true,
      callId,
      status: 'completed',
      duration: voiceCall.duration
    });

  } catch (error: any) {
    console.error('End call error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end call'
    });
  }
});

// Configure multer for voice recording uploads with encryption
const storage = multer.memoryStorage(); // Store in memory for encryption

const upload = multer({ 
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed') as any, false);
    }
  }
});

/**
 * Upload voice call recording with encryption
 * POST /api/voice/upload-recording
 */
router.post('/upload-recording', upload.single('recording'), async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId, duration, morphingProfile } = req.body;
    const file = req.file;

    console.log('🎤 Voice recording upload:', { callId, duration, fileSize: file?.size });

    if (!file || !callId) {
      res.status(400).json({
        success: false,
        error: 'Recording file and call ID are required'
      });
      return;
    }

    // Find the voice call record
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Voice call not found'
      });
      return;
    }

    // SECURITY: Encrypt the recording before storing
    const SecureKeyStorage = require('../../utils/secure-key-storage').default;
    const { encryptFile } = require('../../utils/encryption');

    // Create secure encryption keys
    const keyData = SecureKeyStorage.createFileEncryptionKey();
    const encryptedBuffer = await encryptFile(file.buffer, keyData.key.toString('hex'));

    // Create secure storage path
    const uploadDir = path.join(__dirname, '../../uploads/recordings/encrypted');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const encryptedName = `${crypto.randomUUID()}_${Date.now()}.enc`;
    const encryptedPath = path.join(uploadDir, encryptedName);

    // Save encrypted file
    await fs.promises.writeFile(encryptedPath, encryptedBuffer.encrypted);

    // Update voice call with encrypted recording info
    voiceCall.recordingPath = encryptedPath;
    voiceCall.recordingSize = file.size;
    voiceCall.duration = parseInt(duration) || 0;
    voiceCall.metadata = {
      ...voiceCall.metadata,
      encryptionKey: keyData.encryptedStorage,
      adminAccessKey: keyData.adminAccess,
      fileIv: encryptedBuffer.iv,
      fileTag: encryptedBuffer.tag,
      originalMimeType: file.mimetype
    };
    await voiceCall.save();

    // Update user's voiceRecordings
    if (voiceCall.callerId) {
      const user = await UserModel.findById(voiceCall.callerId);
      if (user) {
        if (!user.voiceRecordings) user.voiceRecordings = [];
        user.voiceRecordings.push({
          recordingId: voiceCall._id.toString(),
          sessionId: callId,
          timestamp: new Date(),
          duration: voiceCall.duration || 0,
          fileSize: file.size,
          encryptionKey: '', // Not available here
          isProcessed: true,
          voiceProfile: morphingProfile || 'ANONYMOUS'
        });
        await user.save();
      }
    }

    // Log for admin panel
    await AuditLogModel.create({
      action: 'VOICE_RECORDING_UPLOADED',
      userId: voiceCall.callerId,
      details: {
        callId,
        recordingPath: file.path,
        recordingSize: file.size,
        duration: voiceCall.duration,
        morphingProfile
      },
      severity: 'low',
      category: 'voice_communication',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Recording uploaded successfully',
      callId,
      recordingId: voiceCall._id
    });

  } catch (error) {
    console.error('❌ Voice recording upload failed:', error);
    res.status(500).json({
      success: false,
      error: 'Recording upload failed'
    });
  }
});

/**
 * Save voice call metadata
 * POST /api/voice/save-call-metadata
 */
router.post('/save-call-metadata', async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      callId,
      callerId,
      recipientId,
      startTime,
      endTime,
      duration,
      morphingProfile,
      recordingPath,
      status,
      callerIP,
      deviceFingerprint,
      bleDeviceId,
      callQuality,
      metadata
    } = req.body;

    console.log('🎤 Saving call metadata:', { callId, callerId, recipientId });

    // Create or update voice call record
    const voiceCall = await VoiceCallModel.findOneAndUpdate(
      { callId },
      {
        callId,
        callerId: callerId || 'unknown',
        recipientId: recipientId || 'superuser',
        startTime: new Date(startTime),
        endTime: endTime ? new Date(endTime) : undefined,
        duration: duration || 0,
        status: status || 'ended',
        morphingProfile: morphingProfile || 'agent',
        recordingPath,
        callerIP: callerIP || req.ip,
        deviceFingerprint: deviceFingerprint || 'unknown',
        bleDeviceId,
        callQuality: callQuality || 'good',
        metadata: {
          callerUserAgent: req.get('User-Agent') || 'unknown',
          networkType: metadata?.networkType || 'unknown',
          encryptionLevel: metadata?.encryptionLevel || 'AES-256',
          compressionRatio: metadata?.compressionRatio || 0.7,
          audioCodec: metadata?.audioCodec || 'AAC'
        },
        flaggedForReview: false
      },
      { upsert: true, new: true }
    );

    // Log for admin panel
    await AuditLogModel.create({
      action: 'VOICE_CALL_COMPLETED',
      userId: callerId || 'unknown',
      details: {
        callId,
        recipientId,
        duration,
        morphingProfile,
        recordingAvailable: !!recordingPath,
        callQuality
      },
      severity: 'low',
      category: 'voice_communication',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Call metadata saved successfully',
      callId: voiceCall.callId,
      recordId: voiceCall._id
    });

  } catch (error) {
    console.error('❌ Save call metadata failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save call metadata'
    });
  }
});

/**
 * Get voice calls for admin panel
 * GET /api/voice/admin/calls
 */
router.get('/admin/calls', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const status = req.query.status as string;
    const flagged = req.query.flagged === 'true';
    const dateFrom = req.query.dateFrom as string;
    const dateTo = req.query.dateTo as string;

    // Build filter
    const filter: any = {};
    if (status) filter.status = status;
    if (flagged !== undefined) filter.flaggedForReview = flagged;
    if (dateFrom || dateTo) {
      filter.startTime = {};
      if (dateFrom) filter.startTime.$gte = new Date(dateFrom);
      if (dateTo) filter.startTime.$lte = new Date(dateTo);
    }

    // Get calls with pagination
    const calls = await (VoiceCallModel as IVoiceCallModel).getCallsForAdmin(filter, page, limit);
    const totalCalls = await VoiceCallModel.countDocuments(filter);

    // Get analytics
    const analytics = await (VoiceCallModel as IVoiceCallModel).getCallAnalytics({
      startDate: dateFrom ? new Date(dateFrom) : undefined,
      endDate: dateTo ? new Date(dateTo) : undefined
    });

    res.json({
      success: true,
      data: {
        calls,
        pagination: {
          page,
          limit,
          total: totalCalls,
          pages: Math.ceil(totalCalls / limit)
        },
        analytics: analytics[0] || {
          totalCalls: 0,
          completedCalls: 0,
          failedCalls: 0,
          averageDuration: 0,
          totalDuration: 0,
          recordingCount: 0,
          flaggedCount: 0
        }
      }
    });

  } catch (error) {
    console.error('❌ Get admin calls failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve call data'
    });
  }
});

/**
 * Get voice call recording for admin playback
 * GET /api/voice/admin/recording/:callId
 */
router.get('/admin/recording/:callId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;

    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall || !voiceCall.recordingPath) {
      res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
      return;
    }

    // Check if file exists
    if (!fs.existsSync(voiceCall.recordingPath)) {
      res.status(404).json({
        success: false,
        error: 'Recording file not found'
      });
      return;
    }

    // Log access for audit
    await AuditLogModel.create({
      action: 'VOICE_RECORDING_ACCESSED',
      userId: (req as any).user?.id || 'admin',
      details: {
        callId,
        recordingPath: voiceCall.recordingPath,
        accessedBy: (req as any).user?.username || 'admin'
      },
      severity: 'medium',
      category: 'admin_access',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Stream the audio file
    const stat = fs.statSync(voiceCall.recordingPath);
    const fileSize = stat.size;
    const range = req.headers.range;

    if (range) {
      // Support range requests for audio streaming
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;
      const file = fs.createReadStream(voiceCall.recordingPath, { start, end });
      const head = {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': 'audio/mpeg',
      };
      res.writeHead(206, head);
      file.pipe(res);
    } else {
      const head = {
        'Content-Length': fileSize,
        'Content-Type': 'audio/mpeg',
      };
      res.writeHead(200, head);
      fs.createReadStream(voiceCall.recordingPath).pipe(res);
    }

  } catch (error) {
    console.error('❌ Get recording failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve recording'
    });
  }
});

/**
 * Flag call for review
 * POST /api/voice/admin/flag/:callId
 */
router.post('/admin/flag/:callId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;
    const { flagged, adminNotes } = req.body;

    const voiceCall = await VoiceCallModel.findOneAndUpdate(
      { callId },
      {
        flaggedForReview: flagged,
        adminNotes,
        reviewedBy: (req as any).user?.username || 'admin',
        reviewedAt: new Date()
      },
      { new: true }
    );

    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Call not found'
      });
      return;
    }

    // Log admin action
    await AuditLogModel.create({
      action: flagged ? 'VOICE_CALL_FLAGGED' : 'VOICE_CALL_UNFLAGGED',
      userId: (req as any).user?.id || 'admin',
      details: {
        callId,
        adminNotes,
        reviewedBy: (req as any).user?.username || 'admin'
      },
      severity: 'medium',
      category: 'admin_action',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: `Call ${flagged ? 'flagged' : 'unflagged'} successfully`,
      call: (voiceCall as any).getCallStats()
    });

  } catch (error) {
    console.error('❌ Flag call failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update call flag'
    });
  }
});

/**
 * Get voice modulation settings
 * GET /api/voice/modulation-settings
 */
router.get('/modulation-settings', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;

    // Get global voice modulation settings
    let settings = await VoiceProfileModel.findOne({ type: 'global', userId: null });
    
    if (!settings) {
      // Create default settings
      settings = await VoiceProfileModel.create({
        type: 'global',
        userId: null,
        profile: getDefaultVoiceSettings(),
        updatedBy: adminId,
        updatedAt: new Date()
      });
    }

    // Log admin access
    await AuditLogModel.create({
      action: 'VOICE_SETTINGS_ACCESSED',
      adminId,
      details: {
        settingsType: 'global'
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        settings: settings.profile,
        lastUpdated: settings.updatedAt,
        updatedBy: settings.updatedBy
      }
    });

  } catch (error: any) {
    console.error('Error fetching voice modulation settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch voice modulation settings',
      details: error.message
    });
  }
});

/**
 * Update voice modulation settings
 * PUT /api/voice/modulation-settings
 */
router.put('/modulation-settings', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { settings } = req.body;

    if (!settings || typeof settings !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Settings object is required'
      });
      return;
    }

    // Validate voice settings
    const validationResult = validateVoiceSettings(settings);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid voice modulation settings',
        details: validationResult.errors
      });
    }

    // Get current settings
    let voiceProfile = await VoiceProfileModel.findOne({ type: 'global', userId: null });
    const oldSettings = voiceProfile ? { ...voiceProfile.profile } : {};

    if (!voiceProfile) {
      // Create new settings
      voiceProfile = new VoiceProfileModel({
        type: 'global',
        userId: null,
        profile: settings,
        updatedBy: adminId,
        updatedAt: new Date()
      });
    } else {
      // Update existing settings
      voiceProfile.profile = { ...voiceProfile.profile, ...settings };
      voiceProfile.updatedBy = adminId;
      voiceProfile.updatedAt = new Date();
    }

    await voiceProfile.save();

    // Log admin action
    await AuditLogModel.create({
      action: 'VOICE_SETTINGS_UPDATED',
      adminId,
      details: {
        settingsType: 'global',
        changes: getSettingsChanges(oldSettings, settings),
        affectedSettings: Object.keys(settings)
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Voice modulation settings updated successfully',
      data: {
        settings: voiceProfile.profile,
        updatedAt: voiceProfile.updatedAt,
        changes: getSettingsChanges(oldSettings, settings)
      }
    });

  } catch (error: any) {
    console.error('Error updating voice modulation settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update voice modulation settings',
      details: error.message
    });
  }
});

/**
 * Get user-specific voice profile
 * GET /api/voice/user/:userId/profile
 */
router.get('/user/:userId/profile', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { userId } = req.params;

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid userId format'
      });
      return;
    }

    // Verify user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Get user's voice profile
    let voiceProfile = await VoiceProfileModel.findOne({ type: 'user', userId });
    
    if (!voiceProfile) {
      // Create default user profile based on global settings
      const globalSettings = await VoiceProfileModel.findOne({ type: 'global', userId: null });
      const defaultProfile = globalSettings ? globalSettings.profile : getDefaultVoiceSettings();
      
      voiceProfile = await VoiceProfileModel.create({
        type: 'user',
        userId,
        profile: { ...defaultProfile, enabled: user.isSuperuser || false },
        updatedBy: adminId,
        updatedAt: new Date()
      });
    }

    // Log admin access
    await AuditLogModel.create({
      action: 'USER_VOICE_PROFILE_ACCESSED',
      adminId,
      userId,
      details: {
        username: user.username,
        isSuperuser: user.isSuperuser
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          isSuperuser: user.isSuperuser
        },
        profile: voiceProfile.profile,
        lastUpdated: voiceProfile.updatedAt,
        updatedBy: voiceProfile.updatedBy
      }
    });

  } catch (error: any) {
    console.error('Error fetching user voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user voice profile',
      details: error.message
    });
  }
});

/**
 * Update user-specific voice profile
 * PUT /api/voice/user/:userId/profile
 */
router.put('/user/:userId/profile', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { userId } = req.params;
    const { profile } = req.body;

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid userId format'
      });
      return;
    }

    if (!profile || typeof profile !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Profile object is required'
      });
      return;
    }

    // Verify user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Validate voice settings
    const validationResult = validateVoiceSettings(profile);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid voice profile settings',
        details: validationResult.errors
      });
    }

    // Get current profile
    let voiceProfile = await VoiceProfileModel.findOne({ type: 'user', userId });
    const oldProfile = voiceProfile ? { ...voiceProfile.profile } : {};

    if (!voiceProfile) {
      // Create new profile
      voiceProfile = new VoiceProfileModel({
        type: 'user',
        userId,
        profile: profile,
        updatedBy: adminId,
        updatedAt: new Date()
      });
    } else {
      // Update existing profile
      voiceProfile.profile = { ...voiceProfile.profile, ...profile };
      voiceProfile.updatedBy = adminId;
      voiceProfile.updatedAt = new Date();
    }

    await voiceProfile.save();

    // Log admin action
    await AuditLogModel.create({
      action: 'USER_VOICE_PROFILE_UPDATED',
      adminId,
      userId,
      details: {
        username: user.username,
        changes: getSettingsChanges(oldProfile, profile),
        affectedSettings: Object.keys(profile)
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'User voice profile updated successfully',
      data: {
        user: {
          id: user._id,
          username: user.username,
          isSuperuser: user.isSuperuser
        },
        profile: voiceProfile.profile,
        updatedAt: voiceProfile.updatedAt,
        changes: getSettingsChanges(oldProfile, profile)
      }
    });

  } catch (error: any) {
    console.error('Error updating user voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user voice profile',
      details: error.message
    });
  }
});

/**
 * Get all voice profiles summary
 * GET /api/voice/profiles
 */
router.get('/profiles', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;

    // Get global settings
    const globalProfile = await VoiceProfileModel.findOne({ type: 'global', userId: null });

    // Get all user profiles
    const userProfiles = await VoiceProfileModel.find({ type: 'user' })
      .populate('userId', 'username email isSuperuser status')
      .sort({ updatedAt: -1 });

    // Get users without custom profiles
    const usersWithProfiles = userProfiles
      .filter(p => p.userId)
      .map(p => p.userId!._id.toString());
    const usersWithoutProfiles = await UserModel.find({
      _id: { $nin: usersWithProfiles }
    }).select('username email isSuperuser status');

    // Log admin access
    await AuditLogModel.create({
      action: 'VOICE_PROFILES_SUMMARY_ACCESSED',
      adminId,
      details: {
        totalUserProfiles: userProfiles.length,
        usersWithoutProfiles: usersWithoutProfiles.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        globalProfile: globalProfile ? globalProfile.profile : null,
        userProfiles: userProfiles.map(profile => ({
          user: profile.userId,
          profile: profile.profile,
          lastUpdated: profile.updatedAt
        })),
        usersWithoutProfiles: usersWithoutProfiles.map(user => ({
          id: user._id,
          username: user.username,
          email: user.email,
          isSuperuser: user.isSuperuser,
          status: user.status,
          usingGlobalProfile: true
        }))
      }
    });

  } catch (error: any) {
    console.error('Error fetching voice profiles summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch voice profiles summary',
      details: error.message
    });
  }
});

/**
 * Test voice modulation profile
 * POST /api/voice/test
 */
router.post('/test', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { profile, testType = 'synthetic' } = req.body;

    if (!profile || typeof profile !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Profile object is required'
      });
    }

    // Validate voice settings
    const validationResult = validateVoiceSettings(profile);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid voice profile for testing',
        details: validationResult.errors
      });
    }

    // Generate test results
    const testResults = await generateVoiceTestResults(profile, testType);

    // Log admin action
    await AuditLogModel.create({
      action: 'VOICE_PROFILE_TESTED',
      adminId,
      details: {
        testType,
        profileSettings: profile,
        testResults: testResults.summary
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        testType,
        profile,
        results: testResults
      }
    });

  } catch (error: any) {
    console.error('Error testing voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test voice profile',
      details: error.message
    });
  }
});

// Configure multer for audio file uploads  
const audioUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit for audio files
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed') as any, false);
    }
  }
});

/**
 * Test voice modulation with uploaded audio
 * POST /api/voice/test-modulation
 */
router.post('/test-modulation', authenticateToken, upload.single('audio'), async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { profileName } = req.body;

    if (!req.file) {
      res.status(400).json({
        success: false,
        error: 'Audio file is required'
      });
      return;
    }

    if (!profileName || !VOICE_PROFILES[profileName as keyof typeof VOICE_PROFILES]) {
      res.status(400).json({
        success: false,
        error: 'Valid profile name is required',
        availableProfiles: Object.keys(VOICE_PROFILES)
      });
      return;
    }

    // Check if SoX is available
    const soxAvailable = await voiceModulationService.checkSoxAvailability();
    if (!soxAvailable) {
      res.status(503).json({
        success: false,
        error: 'Voice modulation service unavailable (SoX not found)'
      });
      return;
    }

    // Get the voice profile
    const profile = VOICE_PROFILES[profileName as keyof typeof VOICE_PROFILES];

    // Apply voice modulation
    const modulatedAudio = await voiceModulationService.modulateVoice(
      req.file.buffer,
      profile,
      adminId
    );

    // Log the test
    await AuditLogModel.create({
      action: 'VOICE_MODULATION_TEST',
      adminId,
      details: {
        profileName,
        originalSize: req.file.size,
        modulatedSize: modulatedAudio.length,
        filename: req.file.originalname
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Return modulated audio
    res.set({
      'Content-Type': 'audio/wav',
      'Content-Length': modulatedAudio.length.toString(),
      'Content-Disposition': `attachment; filename="modulated_${profileName}_${Date.now()}.wav"`
    });

    res.send(modulatedAudio);

  } catch (error: any) {
    console.error('Voice modulation test failed:', error);
    res.status(500).json({
      success: false,
      error: 'Voice modulation test failed',
      details: error.message
    });
  }
});

/**
 * Check voice modulation service status
 * GET /api/voice/service-status
 */
router.get('/service-status', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const soxAvailable = await voiceModulationService.checkSoxAvailability();
    
    res.json({
      success: true,
      data: {
        soxAvailable,
        status: soxAvailable ? 'operational' : 'unavailable',
        message: soxAvailable ? 'Voice modulation service is ready' : 'SoX is not installed or not in PATH',
        profiles: Object.keys(VOICE_PROFILES),
        features: [
          'Non-reversible voice morphing',
          'Clear and understandable output',
          'Real-time processing capability',
          'Multiple security profiles'
        ]
      }
    });

  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: 'Failed to check service status'
    });
  }
});

// Helper functions
function getDefaultVoiceSettings() {
  return {
    enabled: true,
    profiles: {
      robotic: {
        pitch: 0.7,
        speed: 1.0,
        distortion: 0.5,
        resonance: 0.3
      },
      chipmunk: {
        pitch: 1.5,
        speed: 1.2,
        distortion: 0.1,
        resonance: 0.8
      },
      deep: {
        pitch: 0.4,
        speed: 0.9,
        distortion: 0.2,
        resonance: 0.6
      },
      alien: {
        pitch: 0.8,
        speed: 1.1,
        distortion: 0.7,
        resonance: 0.4
      }
    },
    superuserDefaults: {
      enabled: true,
      defaultProfile: 'robotic',
      forceModulation: true
    },
    quality: {
      sampleRate: 44100,
      bitDepth: 16,
      bufferSize: 2048
    },
    processing: {
      noiseReduction: true,
      echoCancellation: true,
      autoGainControl: false
    }
  };
}

function validateVoiceSettings(settings: any) {
  const result = {
    isValid: true,
    errors: [] as string[]
  };

  // Validate profiles
  if (settings.profiles) {
    for (const [profileName, profile] of Object.entries(settings.profiles)) {
      if (typeof profile !== 'object') {
        result.errors.push(`Profile ${profileName} must be an object`);
        result.isValid = false;
        continue;
      }

      const p = profile as any;
      
      // Validate pitch (0.1 to 3.0)
      if (p.pitch !== undefined && (typeof p.pitch !== 'number' || p.pitch < 0.1 || p.pitch > 3.0)) {
        result.errors.push(`Profile ${profileName}: pitch must be between 0.1 and 3.0`);
        result.isValid = false;
      }

      // Validate speed (0.5 to 2.0)
      if (p.speed !== undefined && (typeof p.speed !== 'number' || p.speed < 0.5 || p.speed > 2.0)) {
        result.errors.push(`Profile ${profileName}: speed must be between 0.5 and 2.0`);
        result.isValid = false;
      }

      // Validate distortion (0.0 to 1.0)
      if (p.distortion !== undefined && (typeof p.distortion !== 'number' || p.distortion < 0.0 || p.distortion > 1.0)) {
        result.errors.push(`Profile ${profileName}: distortion must be between 0.0 and 1.0`);
        result.isValid = false;
      }

      // Validate resonance (0.0 to 1.0)
      if (p.resonance !== undefined && (typeof p.resonance !== 'number' || p.resonance < 0.0 || p.resonance > 1.0)) {
        result.errors.push(`Profile ${profileName}: resonance must be between 0.0 and 1.0`);
        result.isValid = false;
      }
    }
  }

  // Validate quality settings
  if (settings.quality) {
    const q = settings.quality;
    
    if (q.sampleRate && ![8000, 16000, 22050, 44100, 48000].includes(q.sampleRate)) {
      result.errors.push('Sample rate must be one of: 8000, 16000, 22050, 44100, 48000');
      result.isValid = false;
    }

    if (q.bitDepth && ![8, 16, 24, 32].includes(q.bitDepth)) {
      result.errors.push('Bit depth must be one of: 8, 16, 24, 32');
      result.isValid = false;
    }

    if (q.bufferSize && ![256, 512, 1024, 2048, 4096].includes(q.bufferSize)) {
      result.errors.push('Buffer size must be one of: 256, 512, 1024, 2048, 4096');
      result.isValid = false;
    }
  }

  return result;
}

function getSettingsChanges(oldSettings, newSettings) {
  const changes = {};
  
  for (const key in newSettings) {
    if (JSON.stringify(oldSettings[key]) !== JSON.stringify(newSettings[key])) {
      changes[key] = {
        old: oldSettings[key],
        new: newSettings[key]
      };
    }
  }
  
  return changes;
}

async function generateVoiceTestResults(profile, testType) {
  // Simulate voice modulation testing
  const results = {
    summary: {
      passed: true,
      score: 0,
      issues: [],
      recommendations: []
    },
    details: {
      latency: Math.random() * 50 + 10, // 10-60ms
      quality: Math.random() * 30 + 70, // 70-100%
      recognizability: Math.random() * 40 + 30, // 30-70% (lower is better for anonymity)
      resourceUsage: Math.random() * 20 + 5 // 5-25%
    },
    testType
  };

  // Calculate score based on test results
  let score = 100;
  
  if (results.details.latency > 40) {
    score -= 15;
    results.summary.issues.push('High latency detected');
    results.summary.recommendations.push('Consider reducing buffer size or simplifying processing');
  }

  if (results.details.quality < 80) {
    score -= 10;
    results.summary.issues.push('Audio quality below recommended level');
    results.summary.recommendations.push('Adjust sample rate or bit depth settings');
  }

  if (results.details.recognizability > 60) {
    score -= 20;
    results.summary.issues.push('Voice still too recognizable');
    results.summary.recommendations.push('Increase distortion or adjust pitch more significantly');
  }

  if (results.details.resourceUsage > 20) {
    score -= 5;
    results.summary.issues.push('High resource usage');
    results.summary.recommendations.push('Optimize processing settings for better performance');
  }

  results.summary.score = Math.max(0, score);
  results.summary.passed = score >= 70;

  return results;
}

/**
 * Get all voice calls for admin panel
 * GET /api/voice/calls
 */
router.get('/calls', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const calls = await VoiceCallModel.find()
      .sort({ startTime: -1 })
      .limit(100);

    res.json({
      success: true,
      calls: calls.map(call => ({
        id: call._id,
        callId: call.callId,
        callerId: call.callerId,
        recipientId: call.recipientId,
        startTime: call.startTime,
        endTime: call.endTime,
        duration: call.duration,
        status: call.status,
        morphingProfile: call.morphingProfile,
        recordingPath: call.recordingPath,
        recordingSize: call.recordingSize,
        callerIP: call.callerIP,
        deviceFingerprint: call.deviceFingerprint,
        callQuality: call.callQuality
      }))
    });
  } catch (error) {
    console.error('Error fetching voice calls:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch voice calls'
    });
  }
});

/**
 * Get user voice settings
 * GET /api/voice/user-settings
 */
router.get('/user-settings', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const UserModel = require('../../models/User').default;
    const users = await UserModel.find({ status: 'active' });

    const settings = users.map((user: any) => ({
      userId: user._id.toString(),
      username: user.username,
      defaultMorphingProfile: user.voiceSettings?.defaultMorphingProfile || 'SECURE_MALE',
      voiceCallsEnabled: user.voiceSettings?.voiceCallsEnabled !== false,
      recordingEnabled: user.voiceSettings?.recordingEnabled !== false
    }));

    res.json({
      success: true,
      settings
    });
  } catch (error) {
    console.error('Error fetching user voice settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user voice settings'
    });
  }
});

/**
 * Update user voice profile
 * POST /api/voice/update-user-profile
 */
router.post('/update-user-profile', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, defaultMorphingProfile } = req.body;

    if (!userId || !defaultMorphingProfile) {
      res.status(400).json({
        success: false,
        error: 'User ID and default morphing profile are required'
      });
      return;
    }

    const UserModel = require('../../models/User').default;
    const user = await UserModel.findById(userId);

    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Update user voice settings
    user.voiceSettings = {
      ...user.voiceSettings,
      defaultMorphingProfile
    };

    await user.save();

    res.json({
      success: true,
      message: 'User voice profile updated successfully'
    });
  } catch (error) {
    console.error('Error updating user voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user voice profile'
    });
  }
});

/**
 * Download voice call recording (decrypted for admin)
 * GET /api/voice/recording/:callId
 */
router.get('/recording/:callId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;

    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall || !voiceCall.recordingPath) {
      res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
      return;
    }

    const fs = require('fs');
    const path = require('path');

    const recordingPath = path.resolve(voiceCall.recordingPath);

    if (!fs.existsSync(recordingPath)) {
      res.status(404).json({
        success: false,
        error: 'Recording file not found on disk'
      });
      return;
    }

    // SECURITY: Decrypt the recording for admin access
    if (voiceCall.metadata?.encryptionKey && voiceCall.metadata?.adminAccessKey) {
      try {
        const SecureKeyStorage = require('../../utils/secure-key-storage').default;
        const { decryptFile } = require('../../utils/encryption');

        // Read encrypted file
        const encryptedBuffer = await fs.promises.readFile(recordingPath);

        // Decrypt using admin access key
        const decryptionKey = SecureKeyStorage.decryptKeyWithAdminAccess(
          voiceCall.metadata.adminAccessKey,
          process.env.ADMIN_MASTER_KEY || 'fallback-admin-key'
        );

        // Decrypt file
        const decryptedBuffer = await decryptFile(
          {
            encrypted: encryptedBuffer,
            iv: voiceCall.metadata.fileIv,
            tag: voiceCall.metadata.fileTag
          },
          decryptionKey.toString('hex')
        );

        // Send decrypted audio
        res.setHeader('Content-Type', voiceCall.metadata.originalMimeType || 'audio/wav');
        res.setHeader('Content-Disposition', `attachment; filename="call_${callId}.wav"`);
        res.send(decryptedBuffer);

      } catch (decryptError) {
        console.error('Failed to decrypt recording:', decryptError);
        res.status(500).json({
          success: false,
          error: 'Failed to decrypt recording'
        });
      }
    } else {
      // Legacy: unencrypted recording
      res.setHeader('Content-Type', 'audio/wav');
      res.setHeader('Content-Disposition', `attachment; filename="call_${callId}.wav"`);

      const fileStream = fs.createReadStream(recordingPath);
      fileStream.pipe(res);
    }
  } catch (error) {
    console.error('Error downloading recording:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download recording'
    });
  }
});

export default router;
