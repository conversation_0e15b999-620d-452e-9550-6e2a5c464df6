/**
 * Clean & Minimal Attachment Picker
 * Gallery, Documents, Audio Files only (NO voice notes)
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
} from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import * as Haptics from 'expo-haptics';
import IsolatedMediaService from '../services/IsolatedMediaService';
import { MediaAttachment } from '../services/MediaService';

export interface AttachmentOption {
  id: string;
  title: string;
  color: string;
}

export interface AttachmentPickerProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (attachment: MediaAttachment | MediaAttachment[]) => void;
}



export const AttachmentPicker: React.FC<AttachmentPickerProps> = ({
  visible,
  onClose,
  onSelect,
}) => {
  const slideAnim = React.useRef(new Animated.Value(300)).current;
  const isolatedMediaService = React.useRef(new IsolatedMediaService()).current;

  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 65,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Modern, polished options with isolated media - NO voice notes, NO camera
  const attachmentOptions: AttachmentOption[] = [
    {
      id: 'gallery',
      title: 'Photo Library',
      color: '#007AFF',
    },
    {
      id: 'document',
      title: 'Files',
      color: '#FF9500',
    },
  ];

  const handleOptionPress = async (option: AttachmentOption) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    try {
      switch (option.id) {
        case 'gallery':
          // Use isolated media service for limited gallery access (Instagram-like)
          // Support multiple image selection (max 10)
          const galleryResult = await isolatedMediaService.selectFromLibrary({
            allowsEditing: false,
            quality: 0.8,
            mediaTypes: 'images', // Only images for multiple selection
            allowsMultipleSelection: true,
          });

          if (galleryResult) {
            onSelect(galleryResult);
            onClose();
          }
          return;

        case 'document':
          // Use isolated media service for documents
          const documentAttachment = await isolatedMediaService.selectDocument();

          if (documentAttachment) {
            onSelect(documentAttachment);
            onClose();
          }
          return;

        case 'audio':
          // Use document picker for audio files (no voice recording)
          const audioResult = await DocumentPicker.getDocumentAsync({
            type: [
              'audio/mpeg',
              'audio/wav',
              'audio/mp4',
              'audio/m4a',
              'audio/aac',
            ],
            copyToCacheDirectory: false,
            multiple: false,
          });

          if (!audioResult.canceled && audioResult.assets && audioResult.assets.length > 0) {
            const asset = audioResult.assets[0];
            const audioAttachment: MediaAttachment = {
              id: `audio_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
              name: asset.name,
              type: 'audio',
              size: asset.size || 0,
              uri: asset.uri,
              mimeType: asset.mimeType || 'audio/mpeg',
              isImage: false,
              isVideo: false,
              isAudio: true,
            };

            onSelect(audioAttachment);
            onClose();
          }
          return;

        default:
          return;
      }
    } catch (error) {
      console.error('Attachment picker error:', error);
      alert('Failed to select attachment');
    }
  };



  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={onClose}
        />
        
        <Animated.View 
          style={[
            styles.container,
            { transform: [{ translateY: slideAnim }] }
          ]}
        >
          <View style={styles.handle} />
          
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Add Attachment</Text>
          </View>

          <View style={styles.content}>
            {attachmentOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={styles.option}
                onPress={() => handleOptionPress(option)}
                activeOpacity={0.8}
              >
                <View style={[styles.optionIcon, { backgroundColor: option.color }]} />
                <Text style={styles.optionTitle}>{option.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingBottom: 34,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: '#D1D1D6',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 20,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    textAlign: 'center',
    letterSpacing: -0.4,
  },
  content: {
    paddingHorizontal: 24,
    gap: 12,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  optionIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 16,
  },
  optionTitle: {
    fontSize: 17,
    fontWeight: '500',
    color: '#1C1C1E',
    letterSpacing: -0.4,
  },
});
