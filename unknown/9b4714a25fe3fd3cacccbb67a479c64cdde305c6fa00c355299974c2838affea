import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import Button from '../../components/admin/Button';
import Icon from '../../components/admin/Icon';
import { apiClient } from '../../utils/axiosClient';

interface VoiceProfile {
  pitch: number;
  tempo: number;
  reverb: number;
  distortion: number;
  formant: number;
  chorus: boolean;
  normalize: boolean;
}

interface ServiceStatus {
  soxAvailable: boolean;
  status: string;
  message: string;
  profiles: string[];
  features: string[];
}



interface UserVoiceSettings {
  userId: string;
  username: string;
  defaultMorphingProfile: string;
  voiceCallsEnabled: boolean;
  recordingEnabled: boolean;
}

const VoiceModulationPage: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<string>('SECURE_MALE');
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [availableProfiles, setAvailableProfiles] = useState<Record<string, VoiceProfile>>({});
  const [isLoading, setIsLoading] = useState(true);

  // User voice settings management
  const [userVoiceSettings, setUserVoiceSettings] = useState<UserVoiceSettings[]>([]);
  const [activeTab, setActiveTab] = useState<'testing' | 'settings'>('testing');

  useEffect(() => {
    fetchServiceStatus();
    fetchAvailableProfiles();
    fetchUserVoiceSettings();
  }, []);
  const fetchServiceStatus = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.backend.get('/api/voice/service-status');
      if (response.data.success) {
        setServiceStatus(response.data.data);
        if (!response.data.data.soxAvailable) {
          setError('Voice modulation service is not available. SoX is not installed on the server.');
        }
      }
    } catch (error) {
      console.error('Failed to fetch service status:', error);
      setError('Failed to check voice modulation service status.');
    } finally {
      setIsLoading(false);
    }
  };
  const fetchAvailableProfiles = async () => {
    try {
      const response = await apiClient.backend.get('/api/voice/profiles');
      if (response.data.success) {
        setAvailableProfiles(response.data.data.profiles);
      }
    } catch (error) {
      console.error('Failed to fetch voice profiles:', error);
    }
  };





  // Fetch user voice settings
  const fetchUserVoiceSettings = async () => {
    try {
      const response = await apiClient.backend.get('/api/voice/user-settings');
      setUserVoiceSettings(response.data.settings || []);
    } catch (error: any) {
      console.error('Failed to fetch user voice settings:', error);
    }
  };

  // Update user default voice profile
  const updateUserVoiceProfile = async (userId: string, profileName: string) => {
    try {
      await apiClient.backend.post('/api/voice/update-user-profile', {
        userId,
        defaultMorphingProfile: profileName
      });
      setSuccess(`Updated default voice profile for user`);
      fetchUserVoiceSettings();
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to update user voice profile');
    }
  };



  if (isLoading) {
    return (
      <AdminLayout>
        <Head>
          <title>Voice Modulation | CCALC Admin Panel</title>
        </Head>
        <div className="container">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Icon name="refresh" className="animate-spin text-4xl mb-4 mx-auto" />
              <p>Loading voice modulation service...</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Voice Modulation | CCALC Admin Panel</title>
      </Head>
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Voice Modulation System</h1>
          <p className="text-gray-600">
            Advanced voice morphing using SoX (Sound eXchange) for secure, non-reversible voice modulation.
          </p>
        </div>

        {serviceStatus && (
          <div className="mb-6">
            <div className={`p-4 rounded-lg border ${serviceStatus.soxAvailable 
              ? 'bg-green-50 border-green-200 text-green-800' 
              : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center">
                <Icon name={serviceStatus.soxAvailable ? 'check' : 'warning'} className="mr-3" />
                <div>
                  <h3 className="font-semibold">Service Status: {serviceStatus.status}</h3>
                  <p className="text-sm">{serviceStatus.message}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-800">
              <Icon name="warning" className="mr-3" />
              <span>{error}</span>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center text-green-800">
              <Icon name="check" className="mr-3" />
              <span>{success}</span>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'testing', label: 'Voice Testing', icon: 'check' },
                { id: 'settings', label: 'User Settings', icon: 'user' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon name={tab.icon} className="mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'testing' && (
          <div className="bg-white shadow-lg rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-6">Voice Modulation Testing</h2>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Voice Modulation Profile:
            </label>
            <select
              value={selectedProfile}
              onChange={(e) => setSelectedProfile(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={false}
            >
              {Object.keys(availableProfiles).map((profileName) => (
                <option key={profileName} value={profileName}>
                  {profileName.replace(/_/g, ' ')}
                </option>
              ))}
            </select>
          </div>

          {/* Voice Profile Information */}
          {availableProfiles[selectedProfile] && (
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold mb-3 text-blue-900">Profile Details: {selectedProfile.replace(/_/g, ' ')}</h3>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium text-blue-700">Pitch:</span>
                  <div className="text-blue-900">{availableProfiles[selectedProfile].pitch} semitones</div>
                </div>
                <div>
                  <span className="font-medium text-blue-700">Tempo:</span>
                  <div className="text-blue-900">{availableProfiles[selectedProfile].tempo}x</div>
                </div>
                <div>
                  <span className="font-medium text-blue-700">Reverb:</span>
                  <div className="text-blue-900">{availableProfiles[selectedProfile].reverb}%</div>
                </div>
                <div>
                  <span className="font-medium text-blue-700">Formant:</span>
                  <div className="text-blue-900">{availableProfiles[selectedProfile].formant}Hz</div>
                </div>
              </div>
              <div className="mt-3 text-sm text-blue-700">
                <strong>Security:</strong> Non-reversible voice morphing with real-time processing
              </div>
            </div>
          )}

          {/* Service Status */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3">Voice Modulation Service Status</h3>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-600">SoX Available:</span>
                <div className={`${serviceStatus?.soxAvailable ? 'text-green-600' : 'text-red-600'}`}>
                  {serviceStatus?.soxAvailable ? 'Yes' : 'No'}
                </div>
              </div>
              <div>
                <span className="font-medium text-gray-600">Status:</span>
                <div className="text-gray-900">{serviceStatus?.status || 'Unknown'}</div>
              </div>
              <div>
                <span className="font-medium text-gray-600">Available Profiles:</span>
                <div className="text-gray-900">{serviceStatus?.profiles?.length || 0}</div>
              </div>
            </div>
            {serviceStatus?.message && (
              <div className="mt-3 text-sm text-gray-600">
                <strong>Message:</strong> {serviceStatus.message}
              </div>
            )}
          </div>
          </div>
        )}

        {/* User Settings Tab */}
        {activeTab === 'settings' && (
          <div className="bg-white shadow-lg rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-6">User Voice Settings</h2>

            <div className="space-y-4">
              {userVoiceSettings.map((user) => (
                <div key={user.userId} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">{user.username}</h3>
                      <p className="text-sm text-gray-600">
                        Current Profile: {user.defaultMorphingProfile}
                      </p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <select
                        value={user.defaultMorphingProfile}
                        onChange={(e) => updateUserVoiceProfile(user.userId, e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2"
                      >
                        {serviceStatus?.profiles.map((profile) => (
                          <option key={profile} value={profile}>
                            {profile.replace('_', ' ')}
                          </option>
                        ))}
                      </select>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          user.voiceCallsEnabled
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {user.voiceCallsEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          user.recordingEnabled
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          Recording: {user.recordingEnabled ? 'On' : 'Off'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}


      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(VoiceModulationPage);
