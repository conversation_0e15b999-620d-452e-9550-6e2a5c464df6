/**
 * Chat List Screen Component
 * Shows available chats - superuser for normal users, all users for superuser
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { theme } from '../utils/theme';
import { AuthService } from '../services/AuthService';
import { ChatService } from '../services/ChatService';
import { UserService, User } from '../services/UserService';

export interface ChatListItem {
  id: string;
  username: string;
  displayName: string;
  lastMessage?: string;
  lastMessageTime?: number;
  unreadCount: number;
  isOnline: boolean;
  userType: 'user' | 'superuser';
}

interface ChatListScreenProps {
  onSelectChat: (chatUser: ChatListItem) => void;
  onLogout: () => void;
}

export const ChatListScreen: React.FC<ChatListScreenProps> = ({ 
  onSelectChat, 
  onLogout 
}) => {
  const [chats, setChats] = useState<ChatListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  const authService = AuthService.getInstance();
  const chatService = ChatService.getInstance();
  const userService = UserService.getInstance();

  useEffect(() => {
    initializeChatList();
  }, []);

  const initializeChatList = async () => {
    try {
      setIsLoading(true);
      await loadCurrentUser();
      await loadChatList();
    } catch (error) {
      console.error('Failed to initialize chat list:', error);
      Alert.alert('Error', 'Failed to load chat list');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCurrentUser = async () => {
    try {
      // Get current user info from token
      const token = authService.getToken();
      if (!token) {
        throw new Error('No authentication token');
      }

      // Get user info from AuthService
      const userInfo = authService.getCurrentUser();
      if (userInfo) {
        // Decode JWT token to get additional user info
        try {
          const base64Url = token.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));

          const payload = JSON.parse(jsonPayload);

          setCurrentUser({
            id: userInfo.id,
            username: userInfo.username,
            displayName: payload.displayName || (payload.isSuperuser ? 'Administrator' : userInfo.username),
            isSuperuser: payload.isSuperuser || false,
            status: 'active',
          });
        } catch (decodeError) {
          console.error('Error decoding token:', decodeError);
          // Use basic user info from AuthService
          setCurrentUser({
            id: userInfo.id,
            username: userInfo.username,
            displayName: userInfo.username,
            isSuperuser: false,
            status: 'active',
          });
        }
      } else {
        console.error('No user info available from AuthService');
      }
    } catch (error) {
      console.error('Error loading current user:', error);
    }
  };

  const loadChatList = async () => {
    try {
      const result = await chatService.getChatUsers();
      
      if (result.success && result.users) {
        // Convert API response to ChatListItem format
        const chatItems: ChatListItem[] = result.users.map(user => ({
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          lastMessage: 'No messages yet',
          lastMessageTime: user.lastActive ? new Date(user.lastActive).getTime() : Date.now(),
          unreadCount: 0,
          isOnline: user.isOnline || true,
          userType: user.isSuperuser ? 'superuser' : 'user',
        }));

        setChats(chatItems);
        
        // Update current user info if available
        if (result.currentUser) {
          setCurrentUser({
            id: result.currentUser.id,
            username: result.currentUser.username,
            displayName: result.currentUser.displayName,
            isSuperuser: result.currentUser.isSuperuser,
            status: result.currentUser.status,
          });
        }
      } else {
        console.error('Failed to load chat users:', result.error);
        // Fallback to empty list
        setChats([]);
      }
    } catch (error) {
      console.error('Error loading chat list:', error);
      // Fallback to empty list
      setChats([]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadChatList();
    setRefreshing(false);
  };

  const formatLastMessageTime = (timestamp?: number) => {
    if (!timestamp) return '';
    
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return 'Now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
    return `${Math.floor(diff / 86400000)}d`;
  };

  const handleChatPress = (chat: ChatListItem) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSelectChat(chat);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await authService.logout();
            onLogout();
          },
        },
      ]
    );
  };

  const renderChatItem = ({ item }: { item: ChatListItem }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => handleChatPress(item)}
      activeOpacity={0.6}
    >
      <View style={styles.chatAvatar}>
        <Text style={styles.chatAvatarText}>
          {item.displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
        </Text>
      </View>

      <View style={styles.chatInfo}>
        <View style={styles.chatHeader}>
          <Text style={styles.chatDisplayName}>{item.displayName}</Text>
          <Text style={styles.chatTime}>
            {formatLastMessageTime(item.lastMessageTime)}
          </Text>
        </View>

        {/* Only show message preview if there are actual messages */}
        {item.lastMessage && item.lastMessage !== 'No messages yet' && (
          <Text style={styles.chatLastMessage} numberOfLines={2}>
            {item.lastMessage}
          </Text>
        )}

        {/* Show unread count only if > 0 */}
        {item.unreadCount > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadCount}>{item.unreadCount}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading chats...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Messages</Text>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
          activeOpacity={0.7}
        >
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Chat List */}
      <FlatList
        data={chats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        style={styles.chatList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />

      {/* Empty State */}
      {chats.length === 0 && (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No chats available</Text>
          <Text style={styles.emptySubtext}>
            {currentUser?.isSuperuser 
              ? 'Users will appear here when they start conversations'
              : 'Contact the administrator to start a conversation'
            }
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.secondaryLabel,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1C1C1E',
    letterSpacing: -0.5,
  },
  logoutButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: '#FF3B30',
  },
  logoutButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: 'white',
  },
  chatList: {
    flex: 1,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 0,
  },
  chatAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chatAvatarText: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    letterSpacing: 0.5,
  },
  chatInfo: {
    flex: 1,
    position: 'relative',
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  chatDisplayName: {
    fontSize: 17,
    fontWeight: '600',
    color: '#1C1C1E',
    flex: 1,
    letterSpacing: -0.2,
  },
  chatTime: {
    fontSize: 13,
    color: '#8E8E93',
    fontWeight: '500',
    marginLeft: 12,
  },
  unreadBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    minWidth: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  unreadCount: {
    fontSize: 12,
    fontWeight: '700',
    color: 'white',
  },
  chatLastMessage: {
    fontSize: 15,
    color: '#8E8E93',
    lineHeight: 20,
    marginTop: 2,
  },
  separator: {
    height: 0.5,
    backgroundColor: '#E5E5EA',
    marginLeft: 88,
    opacity: 0.6,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 48,
    paddingTop: -60,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 12,
    textAlign: 'center',
    letterSpacing: -0.3,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.secondaryLabel,
    textAlign: 'center',
    lineHeight: 20,
  },
});

// ChatListScreen is already exported above
