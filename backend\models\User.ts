import mongoose, { Document, Schema } from 'mongoose';

// Enhanced User model for comprehensive tracking and monitoring
export interface I<PERSON><PERSON> extends Document {
  username: string;
  email?: string;
  expressionHash: string; // Hashed unlock expression
  unlockExpression?: string; // Plain text expression (for admin viewing)
  expressionType?: 'calculator' | 'pattern'; // Type of unlock expression
  expressionUpdatedAt?: Date; // When expression was last updated
  profile: {
    displayName: string;
  };
  deviceFingerprintHash?: string; // Hashed device fingerprint (optional until first login)
  deviceMetadata?: {
    model?: string;
    os?: string;
    registeredAt?: Date;
    registrationCoords?: { lat: number; lng: number };
  };
  bleUUIDHash?: string; // Hashed BLE UUID (optional until first login)
  status: 'active' | 'inactive' | 'locked' | 'pending_device_registration';
  lastLoginAt?: Date; // For audit/logging
  failedLoginAttempts?: number; // Brute-force protection
  lockUntil?: Date; // Account lockout timestamp
  builds?: mongoose.Types.ObjectId[]; // References to Build documents
  messageExpiry?: Date; // Optional: per-user message expiry policy
  isSuperuser?: boolean; // True if this user is the designated superuser

  // E2E Encryption (ENHANCED: WhatsApp-like encryption)
  encryption?: {
    publicKey?: string;
    keyGeneratedAt?: Date;
    algorithm?: string;
    sessionCount?: number;
  };
  
  // Session and authentication management
  authTokens?: Array<{
    token: string;
    deviceId: string;
    createdAt: Date;
    expiresAt: Date;
    isActive: boolean;
  }>;
  
  activeSessions?: Array<{
    sessionId: string;
    deviceId: string;
    ipAddress: string;
    startedAt: Date;
    lastActivity: Date;
    isActive: boolean;
  }>;
  
  // Math expression for admin display
  mathExpression?: {
    expression: string;
    type: string;
    updatedAt: Date;
  };
  
  // Enhanced security tracking
  devices: Array<{
    deviceId: string;
    fingerprint: string;
    deviceType: 'mobile' | 'tablet' | 'desktop';
    deviceModel?: string;
    os?: string;
    browser?: string;
    lastUsed: Date;
    isActive: boolean;
    bleDevices: Array<{
      deviceId: string;
      deviceName: string;
      pairedAt: Date;
      lastConnected: Date;
      isVerified: boolean;
      adData?: any;
      characteristics?: Record<string, string | null>;
      signature?: string;
      signatureCharUuid?: string;
    }>;
  }>;
  
  loginHistory: Array<{
    timestamp: Date;
    ipAddress: string;
    userAgent: string;
    deviceFingerprint: string;
    location?: {
      country: string;
      city: string;
      coordinates: [number, number];
    };
    success: boolean;
    failureReason?: string;
  }>;
  
  chatHistory: Array<{
    sessionId: string;
    startedAt: Date;
    endedAt?: Date;
    messageCount: number;
    voiceCallDuration: number; // in seconds
    encryptionUsed: boolean;
  }>;

  // Voice modulation settings
  voiceSettings?: {
    defaultMorphingProfile: 'SECURE_MALE' | 'SECURE_FEMALE' | 'ROBOTIC' | 'DEEP_SECURE' | 'ANONYMOUS';
    voiceCallsEnabled: boolean;
    recordingEnabled: boolean;
    customProfiles?: Array<{
      name: string;
      settings: any;
      createdAt: Date;
    }>;
  };

  voiceRecordings: Array<{
    recordingId: string;
    sessionId: string;
    timestamp: Date;
    duration: number; // in seconds
    fileSize: number; // in bytes
    encryptionKey: string;
    isProcessed: boolean;
    voiceProfile: 'SECURE_MALE' | 'SECURE_FEMALE' | 'ROBOTIC' | 'DEEP_SECURE' | 'ANONYMOUS';
  }>;
  
  securityEvents: Array<{
    eventType: 'suspicious_login' | 'device_change' | 'location_change' | 'failed_auth' | 'admin_device_reset' | 'admin_ble_reset' | 'admin_session_reset' | 'admin_user_unlock' | 'admin_expression_reset';
    timestamp: Date;
    details: any;
    severity: 'low' | 'medium' | 'high' | 'critical';
    resolved: boolean;
  }>;
  
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema = new Schema<IUser>(
  {
    username: { type: String, required: true, unique: true, trim: true },
    email: { type: String, trim: true },
    expressionHash: { type: String, required: true },
    unlockExpression: { type: String }, // Plain text for admin viewing
    expressionType: { 
      type: String, 
      enum: ['calculator', 'pattern'], 
      default: 'calculator' 
    },
    expressionUpdatedAt: { type: Date },
    profile: {
      displayName: { type: String, required: true },
    },
    deviceFingerprintHash: { type: String }, // Optional until first login
    deviceMetadata: {
      model: { type: String },
      os: { type: String },
      registeredAt: { type: Date },
      registrationCoords: {
        lat: { type: Number },
        lng: { type: Number },
      },
    },
    bleUUIDHash: { type: String }, // Optional until first login
    status: {
      type: String,
      enum: ['active', 'inactive', 'locked', 'pending_device_registration'],
      default: 'pending_device_registration',
    },
    lastLoginAt: { type: Date },
    failedLoginAttempts: { type: Number, default: 0 },
    lockUntil: { type: Date },
    builds: [{ type: Schema.Types.ObjectId, ref: 'Build' }],
    messageExpiry: { type: Date },
    isSuperuser: { type: Boolean, default: false },

    // E2E Encryption
    encryption: {
      publicKey: String,
      keyGeneratedAt: Date,
      algorithm: String,
      sessionCount: { type: Number, default: 0 },
    },
    
    // Session and authentication management
    authTokens: [{
      token: { type: String, required: true },
      deviceId: { type: String, required: true },
      createdAt: { type: Date, default: Date.now },
      expiresAt: { type: Date, required: true },
      isActive: { type: Boolean, default: true }
    }],
    
    activeSessions: [{
      sessionId: { type: String, required: true },
      deviceId: { type: String, required: true },
      ipAddress: { type: String, required: true },
      startedAt: { type: Date, default: Date.now },
      lastActivity: { type: Date, default: Date.now },
      isActive: { type: Boolean, default: true }
    }],
    
    // Math expression for admin display
    mathExpression: {
      expression: { type: String, required: false, default: '2+2' },
      type: { type: String, required: false, default: 'arithmetic' },
      updatedAt: { type: Date, default: Date.now }
    },
    
    // Enhanced tracking fields
    devices: [{
      deviceId: { type: String, required: true },
      fingerprint: { type: String, required: true },
      deviceType: { type: String, enum: ['mobile', 'tablet', 'desktop'], required: true },
      deviceModel: String,
      os: String,
      browser: String,
      lastUsed: { type: Date, default: Date.now },
      isActive: { type: Boolean, default: true },
      bleDevices: [{
        deviceId: String,
        deviceName: String,
        pairedAt: Date,
        lastConnected: Date,
        isVerified: Boolean,
        adData: Schema.Types.Mixed,
        characteristics: { type: Schema.Types.Mixed },
        signature: String,
        signatureCharUuid: String
      }]
    }],
    
    loginHistory: [{
      timestamp: { type: Date, default: Date.now },
      ipAddress: String,
      userAgent: String,
      deviceFingerprint: String,
      location: {
        country: String,
        city: String,
        coordinates: [Number]
      },
      success: { type: Boolean, default: true },
      failureReason: String
    }],
    
    chatHistory: [{
      sessionId: String,
      startedAt: { type: Date, default: Date.now },
      endedAt: Date,
      messageCount: { type: Number, default: 0 },
      voiceCallDuration: { type: Number, default: 0 }, // in seconds
      encryptionUsed: { type: Boolean, default: true }
    }],

    // Voice modulation settings
    voiceSettings: {
      defaultMorphingProfile: {
        type: String,
        enum: ['SECURE_MALE', 'SECURE_FEMALE', 'ROBOTIC', 'DEEP_SECURE', 'ANONYMOUS'],
        default: 'SECURE_MALE'
      },
      voiceCallsEnabled: { type: Boolean, default: true },
      recordingEnabled: { type: Boolean, default: true },
      customProfiles: [{
        name: String,
        settings: Schema.Types.Mixed,
        createdAt: { type: Date, default: Date.now }
      }]
    },

    voiceRecordings: [{
      recordingId: String,
      sessionId: String,
      timestamp: { type: Date, default: Date.now },
      duration: Number, // in seconds
      fileSize: Number, // in bytes
      encryptionKey: String,
      isProcessed: { type: Boolean, default: false },
      voiceProfile: { type: String, enum: ['SECURE_MALE', 'SECURE_FEMALE', 'ROBOTIC', 'DEEP_SECURE', 'ANONYMOUS'] }
    }],
    
    securityEvents: [{
      eventType: { 
        type: String, 
        enum: [
          'suspicious_login', 
          'device_change', 
          'location_change', 
          'failed_auth', 
          'admin_device_reset', 
          'admin_ble_reset', 
          'admin_session_reset', 
          'admin_user_unlock', 
          'admin_expression_reset'
        ] 
      },
      timestamp: { type: Date, default: Date.now },
      details: Schema.Types.Mixed,
      severity: { type: String, enum: ['low', 'medium', 'high', 'critical'], default: 'medium' },
      resolved: { type: Boolean, default: false }
    }]
  },
  { timestamps: true }
);

export default mongoose.model<IUser>('User', UserSchema);
